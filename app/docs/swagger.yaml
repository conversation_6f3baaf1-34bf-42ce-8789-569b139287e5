consumes:
- application/json
definitions:
  apperrors.ErrorCode:
    enum:
    - access_token_expired
    - all_signals_failed_processing
    - authentication_error
    - authorization_error
    - database_error
    - forbidden
    - invalid_correlation_id
    - internal_error
    - invalid_request
    - invalid_url_param
    - malformed_body
    - not_implemented
    - password_too_short
    - refresh_token_invalid
    - request_too_large
    - rate_limit_exceeded
    - resource_already_exists
    - resource_expired
    - resource_in_use
    - resource_not_found
    - token_invalid
    type: string
    x-enum-varnames:
    - ErrCodeAccessTokenExpired
    - ErrCodeAllSignalsFailedProcessing
    - ErrCodeAuthenticationFailure
    - ErrCodeAuthorizationFailure
    - ErrCodeDatabaseError
    - ErrCodeForbidden
    - ErrCodeInvalidCorrelationID
    - ErrCodeInternalError
    - ErrCodeInvalidRequest
    - ErrCodeInvalidURLParam
    - ErrCodeMalformedBody
    - ErrCodeNotImplemented
    - ErrCodePasswordTooShort
    - ErrCodeRefreshTokenInvalid
    - ErrCodeRequestTooLarge
    - ErrCodeRateLimitExceeded
    - ErrCodeResourceAlreadyExists
    - ErrCodeResourceExpired
    - ErrCodeResourceInUse
    - ErrCodeResourceNotFound
    - ErrCodeTokenInvalid
  auth.AccessTokenResponse:
    properties:
      access_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.33ANor7XHWkB87npB4RWsJUjBnJHdYZce-lT8w_IN_s
        type: string
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      account_type:
        enum:
        - user
        - service_account
        type: string
      expires_in:
        description: seconds
        example: 1800
        type: integer
      isn_perms:
        additionalProperties:
          $ref: '#/definitions/auth.IsnPerms'
        type: object
      role:
        enum:
        - owner
        - admin
        - member
        example: admin
        type: string
      token_type:
        example: Bearer
        type: string
    type: object
  auth.IsnPerms:
    properties:
      permission:
        enum:
        - read
        - write
        example: read
        type: string
      signal_batch_id:
        example: 967affe9-5628-4fdd-921f-020051344a12
        type: string
      signal_types:
        description: list of available signal types for the isn
        example:
        - signal-type-1/v0.0.1
        - signal-type-2/v1.0.0
        items:
          type: string
        type: array
      visibility:
        description: ISN visibility setting
        enum:
        - public
        - private
        example: private
        type: string
    type: object
  handlers.BatchStatus:
    properties:
      failed_count:
        type: integer
      signal_type_slug:
        type: string
      signal_type_version:
        type: string
      stored_count:
        type: integer
      unresolved_failures:
        items:
          $ref: '#/definitions/handlers.FailureRow'
        type: array
    type: object
  handlers.BatchStatusResponse:
    properties:
      account_id:
        type: string
      batch_id:
        type: string
      batch_status:
        items:
          $ref: '#/definitions/handlers.BatchStatus'
        type: array
      closed_at:
        type: string
      contains_failures:
        type: boolean
      created_at:
        type: string
      is_latest:
        type: boolean
      isn_slug:
        type: string
    type: object
  handlers.CreateIsnRequest:
    properties:
      detail:
        example: Sample ISN description
        type: string
      is_in_use:
        example: true
        type: boolean
      title:
        example: Sample ISN @example.org
        type: string
      visibility:
        enum:
        - public
        - private
        example: private
        type: string
    type: object
  handlers.CreateIsnResponse:
    properties:
      id:
        example: ********-3b14-42cf-b785-df28ce570400
        type: string
      resource_url:
        example: http://localhost:8080/api/isn/sample-isn--example-org
        type: string
      slug:
        example: sample-isn--example-org
        type: string
    type: object
  handlers.CreateServiceAccountRequest:
    properties:
      client_contact_email:
        example: <EMAIL>
        type: string
      client_organization:
        example: example org
        type: string
    type: object
  handlers.CreateServiceAccountResponse:
    properties:
      account_id:
        example: 550e8400-e29b-41d4-a716-************
        type: string
      client_id:
        example: sa_example-org_k7j2m9x1
        type: string
      expires_at:
        example: "2024-12-25T10:30:00Z"
        type: string
      expires_in:
        example: 172800
        type: integer
      setup_url:
        example: https://api.example.com/api/auth/service-accounts/setup/550e8400-e29b-41d4-a716-************
        type: string
    type: object
  handlers.CreateSignal:
    properties:
      content:
        type: object
      correlation_id:
        description: optional - supply the id of another signal if you want to link
          to it
        example: 75b45fe1-ecc2-4629-946b-fd9058c3b2ca
        type: string
      local_ref:
        example: item_id_#1
        type: string
    type: object
  handlers.CreateSignalTypeRequest:
    properties:
      bump_type:
        description: this is used to increment semver for the signal type
        enum:
        - major
        - minor
        - patch
        example: patch
        type: string
      detail:
        description: description
        example: description
        type: string
      readme_url:
        description: 'README file URL: must be a GitHub URL ending in .md'
        example: https://github.com/user/project/blob/2025.01.01/readme.md
        type: string
      schema_url:
        description: 'JSON schema URL: must be a GitHub URL ending in .json, OR use
          https://github.com/skip/validation/main/schema.json to disable validation'
        example: https://github.com/user/project/blob/2025.01.01/schema.json
        type: string
      title:
        description: unique title
        example: Sample Signal @example.org
        type: string
    type: object
  handlers.CreateSignalTypeResponse:
    properties:
      resource_url:
        example: http://localhost:8080/api/isn/sample-isn--example-org/signals_types/sample-signal--example-org/v0.0.1
        type: string
      sem_ver:
        example: 0.0.1
        type: string
      slug:
        example: sample-signal--example-org
        type: string
    type: object
  handlers.CreateSignalsBatchResponse:
    properties:
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      resource_url:
        example: http://localhost:8080/api/isn/sample-isn--example-org/account/{account_id}/batch/{signals_batch_id}
        type: string
      signals_batch_id:
        example: b51faf05-aaed-4250-b334-2258ccdf1ff2
        type: string
    type: object
  handlers.CreateSignalsRequest:
    properties:
      signals:
        items:
          $ref: '#/definitions/handlers.CreateSignal'
        type: array
    type: object
  handlers.CreateSignalsResponse:
    properties:
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      isn_slug:
        example: sample-isn--example-org
        type: string
      results:
        $ref: '#/definitions/handlers.CreateSignalsResults'
      signal_type_path:
        example: signal-type-1/v0.0.1
        type: string
      signals_batch_id:
        example: b51faf05-aaed-4250-b334-2258ccdf1ff2
        type: string
      summary:
        $ref: '#/definitions/handlers.CreateSignalsSummary'
    type: object
  handlers.CreateSignalsResults:
    properties:
      failed_signals:
        items:
          $ref: '#/definitions/handlers.FailedSignal'
        type: array
      stored_signals:
        items:
          $ref: '#/definitions/handlers.StoredSignal'
        type: array
    type: object
  handlers.CreateSignalsSummary:
    properties:
      failed_count:
        example: 5
        type: integer
      stored_count:
        example: 95
        type: integer
      total_submitted:
        example: 100
        type: integer
    type: object
  handlers.CreateUserRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        description: passwords must be at least 11 characters long
        example: lkIB53@6O^Y
        type: string
    type: object
  handlers.FailedSignal:
    properties:
      error_code:
        example: validation_error
        type: string
      error_message:
        example: field 'name' is required
        type: string
      local_ref:
        example: item_id_#2
        type: string
    type: object
  handlers.FailureRow:
    properties:
      error_code:
        type: string
      error_message:
        type: string
      local_ref:
        type: string
    type: object
  handlers.GrantIsnAccountPermissionRequest:
    properties:
      permission:
        example: write
        type: string
    type: object
  handlers.Isn:
    properties:
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      detail:
        example: Sample ISN description
        type: string
      id:
        example: ********-3b14-42cf-b785-df28ce570400
        type: string
      is_in_use:
        example: true
        type: boolean
      slug:
        example: sample-isn--example-org
        type: string
      title:
        example: Sample ISN @example.org
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      visibility:
        enum:
        - public
        - private
        example: private
        type: string
    type: object
  handlers.IsnAccount:
    properties:
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      account_role:
        enum:
        - owner
        - admin
        - member
        example: admin
        type: string
      account_type:
        enum:
        - user
        - service_account
        example: user
        type: string
      client_id:
        example: client-123
        type: string
      client_organization:
        example: Example Organization
        type: string
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: ********-3b14-42cf-b785-df28ce570400
        type: string
      is_active:
        example: true
        type: boolean
      isn_id:
        example: ********-3b14-42cf-b785-df28ce570400
        type: string
      permission:
        enum:
        - read
        - write
        example: write
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
    type: object
  handlers.IsnAndLinkedInfo:
    properties:
      isn:
        $ref: '#/definitions/handlers.Isn'
      signal_types:
        items:
          $ref: '#/definitions/handlers.SignalType'
        type: array
      user:
        $ref: '#/definitions/handlers.User'
    type: object
  handlers.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        description: passwords must be at least 11 characters long
        example: lkIB53@6O^Y
        type: string
    type: object
  handlers.PreviousSignalVersion:
    properties:
      content:
        type: object
      created_at:
        type: string
      signal_version_id:
        type: string
      version_number:
        type: integer
    type: object
  handlers.ResetUserPasswordRequest:
    properties:
      new_password:
        description: Admin provides the new password
        type: string
    type: object
  handlers.ResetUserPasswordResponse:
    properties:
      message:
        type: string
    type: object
  handlers.SearchSignal:
    properties:
      account_id:
        type: string
      account_type:
        type: string
      content:
        type: object
      correlated_to_signal_id:
        type: string
      email:
        description: not included in public ISN searches
        type: string
      is_withdrawn:
        type: boolean
      local_ref:
        type: string
      signal_created_at:
        type: string
      signal_id:
        type: string
      signal_version_id:
        type: string
      version_created_at:
        type: string
      version_number:
        type: integer
    type: object
  handlers.SearchSignalResponse:
    properties:
      signals:
        items:
          $ref: '#/definitions/handlers.SearchSignalWithCorrelationsAndVersions'
        type: array
    type: object
  handlers.SearchSignalWithCorrelationsAndVersions:
    properties:
      account_id:
        type: string
      account_type:
        type: string
      content:
        type: object
      correlated_signals:
        items:
          $ref: '#/definitions/handlers.SearchSignal'
        type: array
      correlated_to_signal_id:
        type: string
      email:
        description: not included in public ISN searches
        type: string
      is_withdrawn:
        type: boolean
      local_ref:
        type: string
      previous_signal_versions:
        items:
          $ref: '#/definitions/handlers.PreviousSignalVersion'
        type: array
      signal_created_at:
        type: string
      signal_id:
        type: string
      signal_version_id:
        type: string
      version_created_at:
        type: string
      version_number:
        type: integer
    type: object
  handlers.ServiceAccountDetails:
    properties:
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      client_contact_email:
        example: <EMAIL>
        type: string
      client_id:
        example: client-123
        type: string
      client_organization:
        example: Example Organization
        type: string
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
    type: object
  handlers.ServiceAccountRotateResponse:
    properties:
      client_id:
        example: sa_exampleorg_k7j2m9x1
        type: string
      client_secret:
        example: dGhpcyBpcyBhIHNlY3JldA
        type: string
      expires_at:
        example: "2025-07-05T10:30:00Z"
        type: string
      expires_in:
        description: seconds (1 year)
        example: ********
        type: integer
    type: object
  handlers.ServiceAccountTokenRequest:
    properties:
      client_id:
        example: sa_exampleorg_k7j2m9x1
        type: string
      client_secret:
        example: dGhpcyBpcyBhIHNlY3JldA
        type: string
    type: object
  handlers.SignalType:
    properties:
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      detail:
        example: Sample signal type description
        type: string
      id:
        example: ********-3b14-42cf-b785-df28ce570400
        type: string
      is_in_use:
        example: true
        type: boolean
      readme_url:
        example: https://example.com/readme.md
        type: string
      schema_url:
        example: https://example.com/schema.json
        type: string
      sem_ver:
        example: 1.0.0
        type: string
      slug:
        example: sample-signal-type
        type: string
      title:
        example: Sample Signal Type
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
    type: object
  handlers.SignalTypeDetail:
    properties:
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      detail:
        example: Sample signal type description
        type: string
      id:
        example: ********-3b14-42cf-b785-df28ce570400
        type: string
      is_in_use:
        example: true
        type: boolean
      readme_url:
        example: https://github.com/user/project/blob/2025.01.01/readme.md
        type: string
      schema_url:
        example: https://github.com/user/project/blob/2025.01.01/schema.json
        type: string
      sem_ver:
        example: 1.0.0
        type: string
      slug:
        example: sample-signal-type
        type: string
      title:
        example: Sample Signal Type
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
    type: object
  handlers.StoredSignal:
    properties:
      local_ref:
        example: item_id_#1
        type: string
      signal_id:
        example: b8ded113-ac0e-4a2c-a89f-0876fe97b440
        type: string
      signal_version_id:
        example: 835788bd-789d-4091-96e3-db0f51ccbabc
        type: string
      version_number:
        example: 1
        type: integer
    type: object
  handlers.TransferIsnOwnershipRequest:
    properties:
      new_owner_account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
    type: object
  handlers.UpdateIsnRequest:
    properties:
      detail:
        example: Sample ISN description
        type: string
      is_in_use:
        example: true
        type: boolean
      visibility:
        enum:
        - public
        - private
        example: private
        type: string
    type: object
  handlers.UpdatePasswordRequest:
    properties:
      current_password:
        example: lkIB53@6O^Y
        type: string
      new_password:
        example: ue6U>&X3j570
        type: string
    type: object
  handlers.UpdateSignalTypeRequest:
    properties:
      detail:
        description: updated description
        example: description
        type: string
      is_in_use:
        description: whether this signal type version is actively used
        example: false
        type: boolean
      readme_url:
        description: 'README file URL: must be a GitHub URL ending in .md'
        example: https://github.com/user/project/blob/2025.01.01/readme.md
        type: string
    type: object
  handlers.User:
    properties:
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
    type: object
  handlers.UserDetails:
    properties:
      account_id:
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        type: string
      created_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      email:
        example: <EMAIL>
        type: string
      updated_at:
        example: "2025-06-03T13:47:47.331787+01:00"
        type: string
      user_role:
        enum:
        - owner
        - admin
        - member
        example: admin
        type: string
    type: object
  handlers.WithdrawSignalRequest:
    properties:
      local_ref:
        example: item_id_#1
        type: string
    type: object
  responses.ErrorResponse:
    properties:
      error_code:
        allOf:
        - $ref: '#/definitions/apperrors.ErrorCode'
        example: example_error_code
      message:
        example: message describing the error
        type: string
    type: object
  version.Info:
    properties:
      build_date:
        example: "2025-01-01T12:00:00Z"
        type: string
      git_commit:
        example: abc123
        type: string
      version:
        example: v1.0.0
        type: string
    type: object
info:
  contact: {}
  description: |-
    Signals ISN service API for managing Information Sharing Networks

    ## Common Error Responses
    All endpoints may return:
    - `400` Malformed request (invalid json, missing required fields, etc.)
    - `401` Unauthorized (invalid credentials)
    - `403` Forbidden (insufficient permissions)
    - `413` Request body exceeds size limit
    - `429` Rate limit exceeded
    - `500` Internal server error

    Individual endpoints document their specific business logic errors.

    ## Request Limits
    All endpoints are protected by:
    - **Rate limiting**: Configurable requests per second (default: 100 RPS, 20 burst)
    - **Request size limits**: 64KB for admin/auth endpoints, 5MB for signal ingestion

    Check the X-Max-Request-Body response header for the configured limit on signals payload.

    The rate limit is set globaly and prevents abuse of the service.
    In production there will be additional protections in place such as per-IP rate limiting provided by the load balancer/reverse proxy.

    ## Authentication & Authorization

    ### OAuth
    The signalsd backend service acts as an OAuth 2.0 Authorization Server and supports web users and service accounts.

    ### Authentication Flows
    - **Web users**: (Refresh Token Grant Type) Authentication via /auth/login → receive JWT access token + HTTP-only refresh cookie → use bearer tokens for API calls
    - **Service accounts**: Clients implement OAuth Client Credentials flow → receive JWT access token → use bearer tokens for API calls

    ### Token Usage
    All protected API endpoints require a valid JWT access token in the Authorization header:
    ```
    Authorization: Bearer <jwt-access-token>
    ```

    **Token Refresh (Web Users):**
    - Client calls `/oauth/token?grant_type=refresh_token` with HTTP-only refresh token cookie
    - API validates refresh token and issues new access token + rotated refresh cookie
    - Client receives new bearer token for subsequent API calls

    **Token Refresh (Service Accounts):**
    - Client calls `/oauth/token?grant_type=client_credentials` with client ID/secret
    - API validates credentials and issues new access token
    - Client receives new bearer token for subsequent API calls

    **Token Lifetimes:**
    - Access tokens: 30 minutes
    - Refresh tokens: 30 days (web users only)

    ### CSRF Protection
    The refresh token used by the /oauth API endpoints is stored in an HttpOnly cookie (to prevent access by JavaScript)
    and marked with SameSite=Lax (to prevent it from being sent in cross-site requests, mitigating CSRF).

    ### CORS Protection

    CORS is used to control which browser-based clients can make cross-origin requests to the API and read responses.

    By default the server will start with ALLOWED_ORIGINS=*

    In production, you should restrict ALLOWED_ORIGINS to trusted client origins rather than leaving it as *.

    ## Date/Time Handling:

    **URL Parameters**: The following ISO 8601 formats are accepted in URL query parameters:
    - 2006-01-02T15:04:05Z (UTC)
    - 2006-01-02T15:04:05+07:00 (with offset)
    - 2006-01-02T15:04:05.999999999Z (nano precision)
    - 2006-01-02 (date only, treated as start of day UTC: 2006-01-02T00:00:00Z)

    Note: When including a timestamp with a timezone offset in a query parameter, encode the + sign as %2B (e.g. 2025-08-31T12:00:00%2B07:00). Otherwise, + may be interpreted as a space.

    **Response Bodies**: All date/time fields in JSON responses use RFC3339 format (ISO 8601):
    - Example: "2025-06-03T13:47:47.331787+01:00"
  license:
    name: MIT
  title: Signals ISN API
paths:
  /api/admin/accounts/{account_id}/admin-role:
    delete:
      description: '**This endpoint can only be used by the site owner account**'
      parameters:
      - description: account id
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: account_id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Revoke admin role
      tags:
      - Site admin
    put:
      description: |-
        This endpoint grants the admin role to a site member

        An admin can:
        - Create an ISN
        - Define the signal_types used in the ISN
        - read/write to their own ISNs
        - Grant other accounts read or write access to their ISNs

        Note that admins can't change ISNs they don't own (the site owner must use the `transfer ownership` endpoint if this is requred)

        An admin also has access to the following site admin functions:
        - Create service accounts
        - Disable/Enable accounts
        - View all users and their email addresses
        - Reset user passwords

        **This endpoint can only be used by the site owner account**
      parameters:
      - description: account id
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: account_id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Grant admin role
      tags:
      - Site admin
  /api/admin/accounts/{account_id}/disable:
    post:
      description: |-
        **Use Cases:**
        - **Security Incident**: Compromised account needs immediate lockout
        - **Employee Departure**: Remove access for departed staff

        **Actions Performed:**
        - Sets `is_active = false` (account becomes unusable)
        - Revokes all client secrets/one-time secrets (service accounts)
        - Revokes all refresh tokens (web users)

        **Recovery:** Account must be re-enabled by admin via `/admin/accounts/{id}/enable`
        Service accounts also need re-registration via `/api/auth/register/service-accounts`

        **Note:** The site owner account cannot be disabled to prevent system lockout.
        Only owners and admins can disable accounts.
      parameters:
      - description: Account ID to disable
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: account_id
        required: true
        type: string
      responses:
        "200":
          description: OK
        "400":
          description: Invalid account ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Cannot disable site owner account
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Account not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Disable an account
      tags:
      - Site admin
  /api/admin/accounts/{account_id}/enable:
    post:
      description: |-
        **Administrative endpoint to re-enable previously disabled accounts.**
        Sets account status to `is_active = true` (does not create new tokens).

        **Post-Enable Steps Required:**
        - **Service Accounts**: Must re-register via `/api/auth/register/service-accounts` (same client_id, new credentials)
        - **Web Users**: Can immediately log in again via `/auth/login`

        Only owners and admins can enable accounts.
      parameters:
      - description: Account ID to enable
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: account_id
        required: true
        type: string
      responses:
        "200":
          description: OK
        "400":
          description: Invalid account ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: 'Insufficient permissions '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Account not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Enable an account
      tags:
      - Site admin
  /api/admin/isn/{isn_slug}/transfer-ownership:
    put:
      description: |-
        Transfer ownership of an ISN to another admin account.
        This can be used when an admin leaves or when reorganizing responsibilities.
        Only the site owner can transfer ISN ownership.
      parameters:
      - description: ISN slug
        in: path
        name: isn_slug
        required: true
        type: string
      - description: Transfer details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.TransferIsnOwnershipRequest'
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      - RefreshTokenCookieAuth: []
      summary: Transfer ISN ownership
      tags:
      - ISN configuration
  /api/admin/reset:
    post:
      description: |-
        Delete all registered users and associated data.
        This endpoint only works on environments configured as 'dev'
      responses:
        "200":
          description: OK
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: reset
      tags:
      - Site admin
  /api/admin/service-accounts:
    get:
      description: |-
        Get a list of all service accounts in the system.
        Only owners and admins can view service account lists.
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.ServiceAccountDetails'
            type: array
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: 'Insufficient permissions '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Get all service accounts
      tags:
      - Site admin
  /api/admin/service-accounts/{id}:
    get:
      description: |-
        Get a specific service account by account ID.
        Only owners and admins can view service account details.
      parameters:
      - description: Service Account ID
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ServiceAccountDetails'
        "400":
          description: Invalid service account ID format
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: 'Insufficient permissions '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Service account not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Get service account
      tags:
      - Site admin
  /api/admin/users:
    get:
      description: |-
        This api displays site users and their email addresses (can only be used by owner and admin accounts)
        No query parameters = return all users
        With query parameters = return specific user: ?id=uuid or ?email=address
      parameters:
      - description: user account ID
        example: 68fb5f5b-e3f5-4a96-8d35-cd2203a06f73
        in: query
        name: id
        type: string
      - description: user email address
        example: <EMAIL>
        in: query
        name: email
        type: string
      responses:
        "200":
          description: Specific user (when query params provided)
          schema:
            $ref: '#/definitions/handlers.UserDetails'
        "400":
          description: Invalid request - cannot provide both id and email parameters
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: 'Insufficient permissions '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Get registered users or specific user
      tags:
      - Site admin
  /api/admin/users/{user_id}/reset-password:
    put:
      description: Allows admins to reset a user's password (use this endpoint if
        the user has forgotten their password)
      parameters:
      - description: User Account ID
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: user_id
        required: true
        type: string
      - description: New password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ResetUserPasswordRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ResetUserPasswordResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden - admin role required
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Reset user password
      tags:
      - Site admin
  /api/auth/login:
    post:
      description: |-
        The response body includes an access token which can be used to access the protected enpoints, assuming the account has the appropriate permissions.
        The access_token is valid for 30 minutes.

        As part of the login response, the server sets a http-only cookie on the client that will allow it to refresh the token (use the /oauth/token endpoint with a grant_type=refresh_token param)
        The refresh_token lasts 30 days unless it is revoked earlier.
        - To renew the refresh_token, log in again.
        - To revoke the refresh_token, call the /oauth/revoke endpoint.

        The account's role and permissions are encoded as part of the jwt access token and this information is also provided in the response body.
      parameters:
      - description: email and password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.LoginRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/auth.AccessTokenResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Login
      tags:
      - auth
  /api/auth/password/reset:
    put:
      description: |
        Self-service endpoint for users to reset their password.  Requires a valid access token and the current password
      parameters:
      - description: user details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdatePasswordRequest'
      responses:
        "204":
          description: No Content
        "400":
          description: 'Bad request with possible error codes: malformed_body, password_too_short'
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Unauthorized with possible error code: authentication_error'
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Password reset
      tags:
      - auth
  /api/auth/register:
    post:
      description: |-
        The first user created is granted the "owner" role and has super-user access to the site.

        Web users can register directly and default to standard member roles.
        New members can't access any information beyond the public data on the site until an admin grants them access to an ISN.

        The site owner can grant other users the admin role.
        Admins can create ISNs and service accounts and grant other accounts permissions to read or write to ISNs they created.
      parameters:
      - description: user details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateUserRequest'
      responses:
        "201":
          description: Created
        "400":
          description: 'Bad request with possible error codes: malformed_body, password_too_short'
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "409":
          description: 'Conflict with possible error code: resource_already_exists'
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Register user
      tags:
      - auth
  /api/auth/register/service-accounts:
    post:
      description: |
        Registring a new service account creates a one time link with the client credentials in it - this must be used by the client within 48 hrs.

        If you want to reissue a client's credentials call this endpoint again with the same client organization and contact email.
        A new one time setup url will be generated and the old one will be revoked.
        Note the client_id will remain the same and any existing client secrets will be revoked.

        You have to be an admin or the site owner to use this endpoint
      parameters:
      - description: service account details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateServiceAccountRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.CreateServiceAccountResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerServiceAccount: []
      summary: Register a new service account
      tags:
      - Service accounts
  /api/auth/service-accounts/rotate-secret:
    post:
      description: |
        Self-service endpoint for service accounts to rotate their client secret.
        This endpoint requires current valid client_id and client_secret for authentication.
        The old secret remains valid for 5 minutes to prevent race conditions when multiple instances are involved and to stop clients being locked out where network issues prevent them from receiving the new secret immediately.

        **Use Cases:**
        - Regular credential rotation for security compliance
        - Suspected credential compromise requiring immediate rotation
      parameters:
      - description: Service account credentials
        in: body
        name: request
        schema:
          $ref: '#/definitions/handlers.ServiceAccountTokenRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ServiceAccountRotateResponse'
        "401":
          description: Authentication failed
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Rotate service account client secret
      tags:
      - auth
  /api/auth/service-accounts/setup/{setup_id}:
    get:
      description: |
        Exchange one-time setup token for permanent client credentials (the one-time request url is created when a new service account is registered).
        the endpoint renders a html page that the user can use to copy their client credentials.
        The setup url is only valid for 48 hours.
      parameters:
      - description: One-time setup ID
        example: 550e8400-e29b-41d4-a716-************
        in: path
        name: setup_id
        required: true
        type: string
      responses:
        "201":
          description: Created
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "410":
          description: Gone
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Complete service account setup
      tags:
      - Service accounts
  /api/isn:
    get:
      description: get a list of the configured ISNs
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.Isn'
            type: array
      summary: Get the ISNs
      tags:
      - ISN details
  /api/isn/:
    post:
      description: |-
        Create an Information Sharing Network (ISN)

        visibility = "private" means that signalsd on the network can only be seen by network participants.

        ISN admins automatically get write permission for their own ISNs.
        Site owners automatically get write permission on all ISNs.

        This endpoint can only be used by the site owner or an admin

        Note there is a cache of public ISNs that is used by the search endpoints. This cache is not dynamically loaded, so adding public ISNs requires a restart of the service
      parameters:
      - description: ISN details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateIsnRequest'
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/handlers.CreateIsnResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      - RefreshTokenCookieAuth: []
      summary: Create an ISN
      tags:
      - ISN configuration
  /api/isn/{isn_slug}:
    get:
      description: Returns details about the ISN
      parameters:
      - description: isn slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.IsnAndLinkedInfo'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Get an ISN configuration
      tags:
      - ISN details
    put:
      description: |-
        Update the ISN details
        This endpoint can only be used by the site owner or the ISN admin
      parameters:
      - description: isn slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: ISN details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateIsnRequest'
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Update an ISN
      tags:
      - ISN configuration
  /api/isn/{isn_slug}/accounts:
    get:
      description: |-
        Get a list of all accounts (users and service accounts) that have permissions on the specified ISN.
        Only ISN admins and site owners can view this information.
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.IsnAccount'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Get all accounts with access to an ISN
      tags:
      - ISN details
  /api/isn/{isn_slug}/accounts/{account_id}:
    put:
      description: |-
        Grant an account read or write access to an isn.
        This end point can only be used by the site owner or the isn admin account.
      parameters:
      - description: permission details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.GrantIsnAccountPermissionRequest'
      - description: isn slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: account id
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: account_id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Grant ISN access permission
      tags:
      - ISN Permissions
  /api/isn/{isn_slug}/batches:
    post:
      description: |
        This endpoint is used by service accounts to create a new batch. Batches are used to track signals sent by an account to the specified ISN.

        Opening a batch closes the previous batch (the client app can decide how long to keep a batch open)

        Signals can only be sent to open batches.

        Authentication is based on the supplied access token:
        the site owner, the isn admin and members with an isn_perm=write can create a batch for the ISN.

        Note this endpoint is not needed for web users (a batch is automatically created when they first write to an isn and is only closed if their permission to write to the ISN is revoked)
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/handlers.CreateSignalsBatchResponse'
      security:
      - BearerAccessToken: []
      summary: Create a new signal batch
      tags:
      - Signal sharing
  /api/isn/{isn_slug}/signal_types:
    get:
      description: Get details for the signal types defined on the ISN
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: signal type slug
        example: sample-signal--example-org
        in: path
        name: signal_type_slug
        required: true
        type: string
      - description: version to be deleted
        example: 0.0.1
        in: path
        name: sem_ver
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.SignalTypeDetail'
            type: array
      summary: Get Signal types
      tags:
      - Signal types
    post:
      description: |
        Signal types specify a record that can be shared over the ISN
        - Each type has a unique title and this is used to create a URL-friendly slug
        - The title and slug fields can't be changed and it is not allowed to reuse a slug that was created by another account.
        - The signal type fields are defined in an external JSON schema file and this schema file is used to validate signals before loading

        Schema URL Requirements
        - Must be a liink to a schema file on a public github repo (e.g., https://github.com/org/repo/blob/2025.01.01/schema.json)
        - To disable schema validation, use the special URL: https://github.com/skip/validation/main/schema.json

        Versions
        - A signal type can have multiple versions - these share the same title/slug but have different JSON schemas
        - Use this endpoint to create the first version - the bump_type (major/minor/patch) determines the initial semver (1.0.0, 0.1.0 or 0.0.1)
        - Subsequent POSTs to this endpoint that reference a previously submitted title/slug but point to a different schema will increment the version based on the supplied bump_type

        Signal type definitions are referred to like this: /api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver} (e.g., /api/isn/sample-isn--example-org/signal_types/sample-signal--example-org/v0.0.1)
      parameters:
      - description: signal type details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateSignalTypeRequest'
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/handlers.CreateSignalTypeResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Create signal type
      tags:
      - Signal types
  /api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}:
    delete:
      description: Only signal types that have never been referenced by signals can
        be deleted
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: signal type slug
        example: sample-signal--example-org
        in: path
        name: signal_type_slug
        required: true
        type: string
      - description: version to be deleted
        example: 0.0.1
        in: path
        name: sem_ver
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Delete signal type
      tags:
      - Signal types
    get:
      description: Returns details about the signal type
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: signal type slug
        example: sample-signal--example-org
        in: path
        name: signal_type_slug
        required: true
        type: string
      - description: version to be recieved
        example: 0.0.1
        in: path
        name: sem_ver
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.SignalTypeDetail'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Get signal type
      tags:
      - Signal types
      - ISN details
    put:
      description: |-
        users can mark the signal type as *in use/not in use* and update the description or link to the readme file
        Signal types marked as 'not in use' are not returned in signal searches and can not receive new signals
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: signal type slug
        example: sample-signal--example-org
        in: path
        name: signal_type_slug
        required: true
        type: string
      - description: Sem ver
        example: 0.0.1
        in: path
        name: sem_ver
        required: true
        type: string
      - description: signal type details to be updated
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateSignalTypeRequest'
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Update signal type
      tags:
      - Signal types
  /api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals:
    post:
      description: |-
        Submit an array of signals for storage on the ISN
        - payloads must not mix signals of different types and are subject to the size limits defined on the site.
        - The client-supplied local_ref must uniquely identify each signal of the specified signal type that will be supplied by the account.
        - If a local reference is received more than once from an account for the specified signal_type a new version of the signal will be stored with a incremented version number.
        - Optionally a correlation_id can be supplied - this will link the signal to a previously received signal. The correlated signal does not need to be owned by the same account but must be in the same ISN.
        - requests are only accepted for the open signal batch for this account/ISN (service accounts need to manually create batches, web users have a batch automatically created when they first write to an ISN).

        **Authentication**

        Requires a valid access token.
        The claims in the access token list the ISNs and signal_types that the account is permitted to use.

        **Error handling**

        if the request is a vaild format but individual signals contain errors (validation errors, incorrect correlation ids, database errors) the errors are recorded in the response but do not prevent other signals from being processed.
        Individual failures are logged and can be tracked using the signals_batch_id returned in the response - see the batch status endpoint.

        Errors that relate to the entire request  - e.g invalid json, authentication, permission and server errors (400, 401, 403, 500) - are not recorded and should be handled by the client immediately.

        **JSON Schema Validation**

        Signals are validated against the JSON schema specified for the signal type unless validation is disabled on the type definition.

        When validation is disabled, basic checks are still done on the incoming data and the following issues create a 400 error and cause the entire payload to be rejected:
        - invalid json format
        - missing fields (the array of signals must be in a json object called signals, and content and local_ref must be present for each record).

        **Signal versions**

        Multiple versions are created when signals are resupplied using the same local_ref, e.g. because the client wants to correct a previously publsihed signal.
        By default search will return the latest version of the signal.
        If a signal has been withdrawn it will be reactivated if you resubmit it using the same local_ref.

        **Correlating signals**

        Correlation IDs can be used to link signals together.  Signals can only be correlated within the same ISN.
        If the supplied correlation_id is not found in the same ISN as the signal being submitted, the response will contain a 422 or 207 status code and the error_code for the failed signal will be `invalid_correlation_id`.

        request level errors (e.g. invalid json, authentication failure etc) return a simple error_code/error_message response rather than a detailed audit log
      parameters:
      - description: isn slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: signal type slug
        example: sample-signal--example-org
        in: path
        name: signal_type_slug
        required: true
        type: string
      - description: signal type sem_ver number
        example: 0.0.1
        in: path
        name: sem_ver
        required: true
        type: string
      - description: create signals
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateSignalsRequest'
      responses:
        "200":
          description: All signals processed successfully
          schema:
            $ref: '#/definitions/handlers.CreateSignalsResponse'
        "207":
          description: Partial success - some signals succeeded, some failed
          schema:
            $ref: '#/definitions/handlers.CreateSignalsResponse'
        "400":
          description: Invalid request format (error_code = malformed_body)
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized request (invalid credentials, error_code = authentication_error)
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden (no permission to write to ISN, error_code = forbidden)
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found (mistyped url or signal_type marked 'not in use')
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "422":
          description: Valid request format but all signals failed processing - returns
            detailed error information
          schema:
            $ref: '#/definitions/handlers.CreateSignalsResponse'
      security:
      - BearerAccessToken: []
      summary: Create signals
      tags:
      - Signal sharing
  /api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals/search:
    get:
      description: |-
        Search for signals by date or account in private ISNs (authentication required - only accounts with read or write permissions to the ISN can access signals).

        Note the endpoint returns the latest version of each signal.
      parameters:
      - description: Start date
        example: "2006-01-02T15:05:00Z"
        in: query
        name: start_date
        type: string
      - description: End date
        example: "2006-01-02T15:15:00Z"
        in: query
        name: end_date
        type: string
      - description: Account ID
        example: def87f89-dab6-4607-95f7-593d61cb5742
        in: query
        name: account_id
        type: string
      - description: Signal ID
        example: 4cedf4fa-2a01-4cbf-8668-6b44f8ac6e19
        in: query
        name: signal_id
        type: string
      - description: Local reference
        example: item_id_#1
        in: query
        name: local_ref
        type: string
      - description: 'Include withdrawn signals (default: false)'
        example: "true"
        in: query
        name: include_withdrawn
        type: string
      - description: 'Include signals that link to each returned signal (default:
          false)'
        example: "true"
        in: query
        name: include_correlated
        type: string
      - description: 'Include previous versions of each returned signal (default:
          false)'
        example: "true"
        in: query
        name: include_previous_versions
        type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.SearchSignalResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Signal Search (private ISNs)
      tags:
      - Signal sharing
  /api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals/withdraw:
    put:
      description: |-
        Withdraw a signal by local reference

        Withdrawn signals are hidden from search results by default but remain in the database.
        Signals can only be withdrawn by the account that created the signal.
        To reactivate a signal resupply it with the same local_ref using the 'create signals' end point.
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: Signal type slug
        example: signal-type-1
        in: path
        name: signal_type_slug
        required: true
        type: string
      - description: Signal type version
        example: 0.0.1
        in: path
        name: sem_ver
        required: true
        type: string
      - description: Withdrawal request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.WithdrawSignalRequest'
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Withdraw a signal
      tags:
      - Signal sharing
  /api/public/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals/search:
    get:
      description: |-
        Search for signals in public ISNs (no authentication required).

        Note the endpoint returns the latest version of each signal.
      parameters:
      - description: Start date
        example: "2006-01-02T15:05:00Z"
        in: query
        name: start_date
        type: string
      - description: End date
        example: "2006-01-02T15:15:00Z"
        in: query
        name: end_date
        type: string
      - description: Account ID
        example: def87f89-dab6-4607-95f7-593d61cb5742
        in: query
        name: account_id
        type: string
      - description: Signal ID
        example: 4cedf4fa-2a01-4cbf-8668-6b44f8ac6e19
        in: query
        name: signal_id
        type: string
      - description: Local reference
        example: item_id_#1
        in: query
        name: local_ref
        type: string
      - description: 'Include withdrawn signals (default: false)'
        example: "true"
        in: query
        name: include_withdrawn
        type: string
      - description: 'Include signals that link to each returned signal (default:
          false)'
        example: "true"
        in: query
        name: include_correlated
        type: string
      - description: 'Include previous versions of each returned signal (default:
          false)'
        example: "true"
        in: query
        name: include_previous_versions
        type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.SearchSignalResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Signal Search (public ISNs)
      tags:
      - Signal sharing
  /api/webhooks:
    post:
      description: register a webhook to recieve signals batch status updates
      responses:
        "204":
          description: Not implemented
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Register webhook (TODO)
      tags:
      - Service accounts
  /health/live:
    get:
      description: Check if the signalsd http service is alive and responding.
      produces:
      - text/plain
      responses:
        "200":
          description: OK - Service is alive
          schema:
            type: string
      summary: Liveness Check
      tags:
      - Health
  /health/ready:
    get:
      description: Check if the signalsd service is ready to accept traffic.
      produces:
      - text/plain
      responses:
        "200":
          description: OK - Service is ready
          schema:
            type: string
        "503":
          description: Service Unavailable - Database connection failed
          schema:
            type: string
      summary: Readiness Check
      tags:
      - Health
  /isn/{isn_slug}/accounts/{account_id}:
    delete:
      description: |-
        Revoke an account read or write access to an isn.
        This end point can only be used by the site owner or the isn admin account.
      parameters:
      - description: isn slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: account id
        example: a38c99ed-c75c-4a4a-a901-c9485cf93cf3
        in: path
        name: account_id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Revoke ISN access permission
      tags:
      - ISN Permissions
  /isn/{isn_slug}/batches/{batch_id}/status:
    get:
      description: |
        Returns the status of a batch, including the number of signals loaded and the number of failures for each signal type

        The endpoint returns the full batch status for the batch

        Where a signal has failed to load as part of the batch and not subsequently been loaded, the failure is considered unresolved and listed as a failure in the batch status

        Note:  Unresolved failures are signals that failed to load in this batch and have not been successfully loaded since the failure occurred.
        If a signal is fixed but subsequently fails again in a later batch, it will be recorded as a new failure, and this new failure will appear in that batch's status.

        Member accounts can see the status of batches that they created.
        ISN Admins can see the status of any batch created for ISNs they administer.
        The site owner can see the status of any batch on the site.
      parameters:
      - description: ISN slug
        example: sample-isn--example-org
        in: path
        name: isn_slug
        required: true
        type: string
      - description: Batch ID
        example: ********-3b14-42cf-b785-df28ce570400
        in: path
        name: batch_id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.BatchStatusResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Get batch processing status
      tags:
      - Signal sharing
  /isn/{isn_slug}/batches/search:
    get:
      description: |
        Search for batches with optional filtering parameters

        The search endpoint returns the full batch status for each batch.

        Where a signal has failed to load as part of the batch and not subsequently been loaded, the failure is considered unresolved and listed as a failure in the batch status

        Member accounts can only see batches they have created. ISN Admins can see batches for ISNs they administer. The site owner can see all batches.

        At least one search criteria must be provided:
        - latest=true (get the latest batch)
        - previous=true (get the previous batch)
        - created date range
        - closed date range
      parameters:
      - description: Get the latest batch
        example: true
        in: query
        name: latest
        type: boolean
      - description: Get the previous batch
        example: true
        in: query
        name: previous
        type: boolean
      - description: Start date for batch creation filtering
        example: "2006-01-02T15:04:05Z"
        in: query
        name: created_after
        type: string
      - description: End date for batch creation filtering
        example: "2006-01-02T16:00:00Z"
        in: query
        name: created_before
        type: string
      - description: Start date for batch closure filtering
        example: "2006-01-02T15:04:05Z"
        in: query
        name: closed_after
        type: string
      - description: End date for batch closure filtering
        example: "2006-01-02T16:00:00Z"
        in: query
        name: closed_before
        type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.BatchStatusResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      security:
      - BearerAccessToken: []
      summary: Search for batches
      tags:
      - Signal sharing
  /oauth/revoke:
    post:
      description: |
        Revoke a refresh token or client secret to prevent it being used to create new access tokens (self-service)

        **Use Cases:**
        - **Web User Logout**: User wants to log out of their session
        - **Service Account Security**: Account no longer being used/compromised secret

        **Service Accounts:**
        You must supply your `client ID` and `client secret` in the request body.
        This revokes all client secrets for the service account, effectively disabling it.

        **IMPORTANT - Service Account Reinstatement:**
        - This endpoint does not permanently disable the service account itself (use `POST /admin/accounts/{account_id}/disable` for that)
        - To restore access, an admin must call `POST /api/auth/register/service-accounts` with the same organization and email
        - This will generate a new setup URL and client secret while preserving the same client_id
        - If the account was disabled by an admin, it must first be re-enabled via `POST /admin/accounts/{account_id}/enable`

        **Web Users (Logout):**
        This endpoint expects a refresh token in an `http-only cookie`.
        This revokes the user's refresh token, effectively logging them out.

        If the refresh token has expired or been revoked, the user must login again to get a new one.

        **Note:** Any unexpired access tokens issued for the account will continue to work until they expire.
      responses:
        "200":
          description: OK
        "400":
          description: 'Invalid request body '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "404":
          description: Token not found or already revoked
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Revoke token
      tags:
      - auth
  /oauth/token:
    post:
      description: |
        **Client Credentials Grant (Service Accounts):**

        Issues new access token (in response body)

        - Set `grant_type=client_credentials` as URL parameter
        - Provide `client_id` and `client_secret` in request body
        - Access tokens expire after 30 minutes
        (subsequent requests using the token will fail with HTTP status 401 and an error_code of "access_token_expired")

        **Refresh Token Grant (Web Users):**

        Issues new access token (in response body) and rotates refresh token (HTTP-only cookie)

        - Set `grant_type=refresh_token` as URL parameter
        - Must have valid refresh token cookie
        - Access tokens expire after 30 minutes
        (subsequent requests using the token will fail with HTTP status 401 and an error_code of "access_token_expired")
        - Refresh tokens expire after 30 days
        - subsequent requests using the refresh token will fail with HTTP status 401 and an error_code of "refresh_token_expired" and users must login again to get a new one.
      parameters:
      - description: grant type
        enum:
        - client_credentials
        - refresh_token
        in: query
        name: grant_type
        required: true
        type: string
      - description: Service account credentials (required for client_credentials
          grant)
        in: body
        name: request
        schema:
          $ref: '#/definitions/handlers.ServiceAccountTokenRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/auth.AccessTokenResponse'
        "400":
          description: 'Invalid grant_type parameter '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
        "401":
          description: 'Authentication failed '
          schema:
            $ref: '#/definitions/responses.ErrorResponse'
      summary: Refresh Access Token
      tags:
      - auth
  /version:
    get:
      description: Returns the current API version details
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/version.Info'
      summary: Get API version
      tags:
      - Site admin
produces:
- application/json
securityDefinitions:
  BearerAccessToken:
    description: Bearer {JWT access token}
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
tags:
- description: Authentication and authorization endpoints.
  name: auth
- description: Site adminstration tools
  name: Site admin
- description: Manage the Information Sharing Networks that are used to exchange signals
    between participating users.
  name: ISN configuration
- description: Grant accounts read or write access to an ISN
  name: ISN Permissions
- description: View information about the configured ISNs
  name: ISN details
- description: Define the format of the data being shared in an ISN
  name: Signal types
- description: Manage service account end points
  name: Service accounts
